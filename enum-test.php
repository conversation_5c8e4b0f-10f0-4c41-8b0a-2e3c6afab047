<?php
enum Color: string {
    case RED = 'red';
    case GREEN = 'green';
    case BLUE = 'blue';
}
// Convert enum to array
$colorArray = Color::cases();
print_r($colorArray);

// Or if you want both name and value
$colorArrayWithNames = array_map(fn($case) => ['name' => $case->name, 'value' => $case->value], Color::cases());
print_r($colorArrayWithNames);

// Or as associative array (name => value)
$colorAssoc = array_column(array_map(fn($case) => [$case->name, $case->value], Color::cases()), 1, 0);
print_r($colorAssoc);
